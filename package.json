{"name": "aspire-mobile-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "fengxiqiu <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "dev:grey": "vue-cli-service serve --mode grey", "dev:prod": "vue-cli-service serve --mode prod", "build": "vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src --fix", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@better-scroll/core": "^2.5.1", "axios": "^1.7.7", "core-js": "^3.37.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "html2canvas": "^1.4.1", "jsencrypt": "^3.3.2", "loadjs": "^4.3.0", "lodash": "^4.17.21", "node-sass": "^8", "nprogress": "^0.2.0", "qrcodejs2": "^0.0.2", "swiper": "^4.5.1", "v-calendar": "^2.4.2", "vant": "^2.13.2", "vue": "2.7.14", "vue-awesome-swiper": "^3.1.3", "vue-barcode": "^1.3.0", "vue-barcode-reader": "^1.0.3", "vue-clipboard2": "^0.3.3", "vue-cookies": "^1.8.4", "vue-directive-touch": "^1.0.28", "vue-qrcode-reader": "^5.5.4", "vue-router": "^3.6.5", "vuex": "^3.6.2", "webpack": "^4", "webpack-cli": "^4"}, "devDependencies": {"@babel/runtime": "^7.24.5", "@vue/babel-preset-app": "^4.5.19", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/compiler-sfc": "^3.4.27", "@vue/composition-api": "^1.7.2", "autoprefixer": "^9.8.8", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.8", "css-loader": "^5.2.7", "eslint": "^6.8.0", "eslint-plugin-vue": "^9.26.0", "html-webpack-plugin": "^4.5.2", "js-cookie": "^3.0.5", "mini-css-extract-plugin": "^1.6.2", "postcss-px-to-viewport": "^1.1.1", "sass-loader": "^10.5.2", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^4", "vue-loader": "^15.11.1", "vue-template-compiler": "2.7.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.19.0"}}
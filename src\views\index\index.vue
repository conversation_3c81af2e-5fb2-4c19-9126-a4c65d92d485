<template>
  <div class="index">
    <LoginDialog
      v-if="!indexData.preview"
      :islogin="indexData.is4gLogined"
      :is-force-login="indexData.isForceLogin"
      :isloginfn="logined"
      :show-cancle="indexData.showCancle"
    />
    <Loading
      v-show="indexData.showLoading"
      size="24px"
      type="spinner"
      vertical
      style="margin-top: 300px"
    >
      加载中...
    </Loading>
    <Shopname v-if="indexData.showShopName && !indexData.showLoading &&indexData.pageData" :content="share.title" />
    <div
      v-if="indexData.pageData"
      class="pageContent"
      :class="{
        'padding-top-34': indexData.showShopName
      }"
    >
      <component
        :is="components[indexData.tplId]"
        :pagedata="indexData.pageData"
        :preview="indexData.preview"
        :logined="indexData.isLogined"
        :act-id="indexData.actId"
        :configure="indexData.configure"
        :user="indexData.myuser"
        :merchant-ctrl="indexData.merchantCtrl"
        :skin="indexData.holidayskin"
      ></component>
      <Share :shopshare="share" order-acid="1" />
    </div>
    <TemplateNew v-if="showNewPage" :shop-data="queryShopData" />
    <template v-if="showFooter">
      <FooterCon
        :list="iconList"
        :user="indexData.myuser"
        :tpl-id="indexData.tplId || queryShopData.tplId"
        isactive="shopindex"
      />
      <div v-myhtml="indexData.myhtml"></div>
      <Buoy :shop-id="indexData.pageInfo" :configure="indexData.configure" />
    </template>
    <!-- <SubMsg ref="submsgrefs" /> -->
    <Submsgdialog
      ref="submsgrefs"
      :show="showMsgDialog.show"
      :shop-id="indexData.pageInfo.shopId"
      @closeDialog="showMsgDialog.show = false"
      @openDialog="showMsgDialog.show = true"
    />
  </div>
</template>
<script setup>
import Submsgdialog from "@/components/home/<USER>"
import Shopname from "@/components/index/headercon.vue"
import Buoy from "@/components/buoy.vue"
import Templatecustom from "./custom.vue"
import Templatenormal from "./normal.vue"
import Templatenormal1 from "./normal_1.vue"
import Templatenormal4 from "./normal_4.vue"
import TemplateNew from "./home.vue"
import shopAPI from "@/api/shop.js"
import { Toast, Loading } from "vant"
import Share from "@/components/index/share.vue"
import FooterCon from "@/components/myfooter.vue"
import loginUtils from "@/utils/login/index.js"
import { setTitle, getAllSearchParamsArray,editCookie,getCookie,copy } from "@/utils/utils.js"
import insertCode from "@/utils/insertCode.js"
import EventBus from "@/api/eventbus.js"
import UA from "@/utils/ua.js"
import {ref,reactive,getCurrentInstance,provide, onMounted,watch, computed,onActivated, onBeforeMount} from "vue"
import LoginDialog from "@/components/login/index.vue"
import PrecisionMarketing from "@/model/precisionMarketing.js"
import cmApi from "@/api/cm"
import aspsc from "@/utils/aspsc"
import login from "@/api/login.js"
import "vant/lib/index.css"
function businessHoursfn(){ return indexData.businessHours}
function shopOpenStatusfn(){
  return indexData.shopOpenStatus
}
function pageInfofn(){
  return indexData.pageInfo
}
function provinceFn(){
  return indexData.provinceId
}
function myShowDatafn(){
  return indexData.myShowData
}
const components = {
  1:Templatecustom,
  2:Templatenormal,
  3:Templatenormal1,
  4:Templatenormal4,
  5:TemplateNew
}

// 下边是给没有修改用的
// provide("pageInfofn",() => indexData.pageInfo)
// provide("openlogin",(callback,showCancle) => {openlogin(callback,showCancle)})
const indexData = reactive({
  myhtml: '',
  holidayskin: null,
  actId: null,
  pageData: null,
  actTitle: null,
  actDesc: null,
  preview: null,
  configure: null,
  isLogined: null,
  is4gLogined: null,
  showCancle:true,
  pageInfo: null,
  myuser: null,
  tplId: null,
  unifiedChannelId: null,
  ua: {},
  shopStatus: 0,
  showLoading: true,
  isForceLogin: false,
  businessHours: null,
  shopOpenStatus: 1,
  showShopName: !UA.isWechat &&
    !UA.isWechatWork &&
    !UA.isApp &&
    !UA.isIosQQ &&
    !UA.isAndroidQQ,
  provinceId:null,
  myShowData:null
})
const cmParam =  {
  cm_code: ['yundian_holiday_skin'],
  province_id: 100,
  city_id: 100,
}
const share = ref({
  shareTitle: '标题标题标题标题标题',
  shareUrl: 'https://www.npmjs.com/package/qrcode',
  shareDec: '',
  shareImg: '',
  title:""
})
const iconList = reactive({
  shopindex: {
    inactive: require('@/assets/my/other.png'),
    active: require('@/assets/my/others-active.png'),
    title: '店铺首页',
    key: 'shopindex',
    links: '',
    isactive: true,
    showLink: true,
    class: 'shopindex',
  },
  onlineshop: {
    inactive: require('@/assets/my/onlineShop.png'),
    active: require('@/assets/my/onlineShop-active.png'),
    title: '在线看店',
    key: 'onlineshop',
    links: '',
    isactive: false,
    showLink: false,
    class: 'onlineshop',
  },
  service: {
    inactive: require('@/assets/my/customerService.png'),
    active: require('@/assets/my/customerService-active.png'),
    title: '客服',
    key: 'service',
    links: '',
    isactive: false,
    showLink: false,
    class: 'service',
  },
  manage: {
    inactive: require('@/assets/my/management.png'),
    active: require('@/assets/my/management-active.png'),
    title: '店铺管理',
    key: 'manage',
    links: '',
    isactive: false,
    showLink: false,
    class: 'manage',
  },
  live: {
    inactive: require('@/assets/my/live.png'),
    active: require('@/assets/my/live-active.png'),
    title: '直播',
    key: 'live',
    links: '',
    isactive: false,
    showLink: false,
    class: 'live',
  },
  my: {
    inactive: require('@/assets/my/my.png'),
    active: require('@/assets/my/my-active.png'),
    title: '我的',
    key: 'my',
    links: '',
    isactive: false,
    showLink: true,
    class: 'my',
  },
})
const getCurrentVue = getCurrentInstance()
const proxy = getCurrentVue ? getCurrentVue.proxy : null
let user
if(proxy){
  user = proxy.$store.getters.user

}
watch(()=>indexData.unifiedChannelId,(val,oldVal)=>{
  if (val && val!==oldVal) {
    //商城插码初始化
    let systemId = indexData.provinceId+"02"
    aspsc.init({uniChannelId:val,shopId:indexData.pageInfo.shopId,systemId})
  }
},{immediate: true})

async function init(){
  const url = new URL(location.href)
  const shopId = url.searchParams.get('shopId')
  const actId = url.searchParams.get('actId')
  const preview = url.searchParams.get('preview')
  const configure = url.searchParams.get('configure')
  if (!shopId && proxy) {
    proxy.$router.push({
      name: 'nearby',
    })
    return false
  }
  indexData.pageInfo = copy({
    shopId,
    actId,
    preview,
    configure,
  })
  indexData.preview = preview
  indexData.configure = configure
  if (indexData.ua.isApp) {
    insertCode('source_app')
  } else if (indexData.ua.isWechat || indexData.ua.isWechatWork) {
    if (!UA.isWechatWork && (await UA.isWeChatMiniApp())) {
      insertCode('source_wechat_miniprograms')
    } else {
      insertCode('source_wechat')
    }
  } else {
    insertCode('source_explorer')
  }
}
onBeforeMount(()=> {
  const url = new URL(location.href)
  const shopId = url.searchParams.get('shopId')
  if (!shopId) {return false}
  if (url.searchParams.has('WT.mc_id')) {
    editCookie('mc_id', url.searchParams.get('WT.mc_id'), '180d', '/')
  }
  if (url.searchParams.get('nalogin') == 1) {
    editCookie('nalogin', 1, '180d', '/')
  }
  if(proxy){
    proxy.$store.commit('SET_SHOPID', shopId)
  }
  if (indexData.preview) {
    loginUtils.login(true, true, logined, false, false, autoLoginCb)
    htmlStyle()
  } else if (indexData.configure) {
    loginUtils.login(true, true, logined, false, true, autoLoginCb)
    document.getElementsByClassName('icpinfo')[0].style.marginBottom = '0'
  } else {
    loginUtils.login(false,false,logined,false,false,autoLoginCb,'0') //用户也可查到信息
    document.getElementsByClassName('icpinfo')[0].style.marginBottom = '68px'
  }
  EventBus.$on('goodsChecked', refresh)
  EventBus.$on('joinGetMdeol', (res) => {
    indexData.pageData = res
  })
})
onMounted(()=>{
  if (!indexData.preview && !indexData.configure){
    document.getElementsByClassName('icpinfo')[0].style.marginBottom = '68px'
  }
})
//每次进入页面都调用
onActivated(()=>{
  document.querySelector('body').setAttribute('class', '')
})
function logined(res) {
  let loginfirst = proxy.$store.getters.loginfirst
  if(loginfirst==1 && UA.isApp){
    checkSubscription() // 检查是否有订阅
  }
  indexData.myuser = res
  indexData.isLogined = res && res.UserName > ''
  queryShopStaTus(indexData.pageInfo.shopId)
  getCmsData()
}
function getLogout() {
  login.logout({}).then((res) => {
    if (res.code == 0) {
      window.localStorage.removeItem("yundianToken")
      proxy.$store.commit("SET_USERINFO", null)
      loginUtils.login(
        false,
        false,
        logined
      )
    }
  })
}
function autoLoginCb(h5ForceLogin, appForceLogin,smsLogin) {
  indexData.isForceLogin = h5ForceLogin
  indexData.is4gLogined = false
  EventBus.$emit('autologin', {h5ForceLogin, appForceLogin,smsLogin})
}
function refresh(r) {
  if (r.code) {
    Toast(r.message)
  } else {
    getData()
  }
}
function configBar() {
  let auth = user.userInfo || {}
  if (
    auth.staffId &&
    indexData.pageInfo &&
    indexData.pageInfo.shopId == auth.shopId
  ) {
    iconList.manage.showLink = true
  } else {
    iconList.manage.showLink = false
  }
}
const showNewPage = ref(false)
const queryShopData = ref(null)
/** 0待上线，1旧首页，2新首页 */
const TPLTYPESTATUS = {
  WAITING:0,
  OLDTPLTYPE:1,
  NEWTPLTYPE : 2
}
function queryShopStaTus(shopId) {
  shopAPI.getShopStatus({ shopId ,queryHomeSwitch:true}).then((r) => {
    let {isNext,shopData} = queryShopStaTusSetData(r)
    queryShopData.value = shopData
    if (isNext) {
      if(queryShopData.value.homePageSwitch==TPLTYPESTATUS.NEWTPLTYPE && indexData.configure!=1){
        showNewPage.value = true
        configBar()
      }else{
        showNewPage.value = false
        getPageData(indexData.pageInfo,shopData)
        getShowData()
      }
    }
  })
}
// 是否展示底部导航
const showFooter = computed(()=>{
  let page = (!indexData.preview && indexData.pageData)
  let showNewPageF = (showNewPage.value && indexData.pageInfo)
  return (page || showNewPageF)
})
function queryShopStaTusSetData(statusRes){
  indexData.showLoading = false
  if (statusRes.code) {
    //一键开店的信息状态是3 已登录且为店主跳到管理页面 其他是无此店铺
    if (statusRes.code == 102) {
      //跳到管理页面
      location.href = '/hd/xskd/index.html?shopId=' + indexData.pageInfo.shopId
    } else {
      Toast(statusRes.message)
    }
    return {isNext:false}
  }

  indexData.shopStatus = statusRes.data.status
  if (statusRes.data.status !== 3) {
    let message = null
    switch (statusRes.data.status) {
    case 0:
      message = '未开通店铺'
      break
    case 1:
      message = '未注册店铺'
      break
    case 2:
      message = '店铺审核中'
      break
    case 5:
      message = '审核未通过'
      break
    case 4:
      message = '店铺已关闭'
      break
    default:
      message = ''
      break
    }

    Toast(message)
    return {isNext:false}
  } else {
    // 热线渠道店铺要提示信息
    if(statusRes.data.shopAttribute == 2){
      Toast('热线渠道店铺, 不可查看')

      indexData.showLoading = false
      return {isNext:false}
    }
    return {isNext:true,shopData:statusRes.data}
  }
}
async function  getData() {
  Promise.all([
    shopAPI.getShopStatus(indexData.pageInfo),
    shopAPI.getActData(indexData.pageInfo),
  ]).then(async([statusRes, actDataRes]) => {
    let {isNext,shopData} = queryShopStaTusSetData(statusRes)
    if(!isNext){
      return false
    }
    if (actDataRes.code) {
      Toast(actDataRes.message)
    } else {
      // 请求精准营销数据
      getIopData(actDataRes)
      setData(actDataRes, shopData)
    }
    indexData.showLoading = false
  })
}
async function getIopData(actDataRes){
  const allParams = getAllSearchParamsArray(location.href)
  const indexTabContentTcClickTrue = sessionStorage.getItem(
    'indexTabContentTcClickTrue'
  )
  if (
    (allParams.marketing ||
      JSON.parse(indexTabContentTcClickTrue)) &&
    (actDataRes.data.tplId == 3 || actDataRes.data.tplId == 4) && !allParams.configure
  ) {
    actDataRes.data.actData = await PrecisionMarketing.joinGetModel(
      actDataRes,
      ()=>{openlogin(null,true)}
    )
  } else {
    sessionStorage.setItem('isDomMealDataLength', false)
  }
}
function getPageData({ shopId, actId, preview, configure },shopData) {
  indexData.showLoading = true
  shopAPI.getActData({ shopId, actId, preview, configure }).then((r) => {
    indexData.showLoading = false
    if (r.code) {
      Toast(r.message)
    } else {
      // 请求精准营销数据
      getIopData(r)
      setData(r,shopData)
    }
  })
}
function setData(actDataRes, shopData) {
  let {
    actTitle,actDec,actData,actId,tplId,shopId,provinceId,keFuPhone,telNumber,
    merchantCtrl, //1:店长配置 2:商户控制
    unifiedChannelId,broadbandStatus,
    layOut,cmData,businessHours,shopOpenStatus,
  } = actDataRes.data
  const {
    shopName,address,shopShortName,appointmentStatus,queueStatus,show,
  } = shopData

  // 加入首页推荐运营位1
  let homeRecommendPrevCur = null
  if (layOut && layOut.forEach) {
    layOut.find((item, index) => {
      if (homeRecommendPrevCur === null && item == 'homeRecommend') {
        homeRecommendPrevCur = layOut[index - 1]
      }
    })
  }
  let homeRecommendIndex = actData.findIndex((item) => {
    if (item.floorSort == homeRecommendPrevCur) {
      return true
    }
  })
  if (cmData.homeRecommend) {
    actData.splice(homeRecommendIndex + 1, 0, {
      componentCode: 'homeRecommend',
      dataList: cmData.homeRecommend,
    })
  }
  indexData.pageData = actData
  indexData.tplId = tplId
  indexData.actTitle = actTitle
  indexData.actId = actId
  indexData.unifiedChannelId = unifiedChannelId
  indexData.provinceId =provinceId
  indexData.merchantCtrl = merchantCtrl
  indexData.businessHours = businessHours
  indexData.shopOpenStatus = shopOpenStatus
  share.value = {
    url: location.href,
    imgUrl: 'https://img0.shop.10086.cn/favicon.png__175.png',
    dcsId: 'yd_sharestore_' + indexData.pageInfo.shopId + '_share',
    desc: shopData.propaganda ? shopData.propaganda : actDec,
    title: shopShortName,
    shortTitle: shopShortName,
    unifiedChannelId,
  }
  if(proxy){
    proxy.$store.commit('SET_PAGEINFO', {
      actTitle,actDec,actId,tplId,shopId,shopName,address,
      appointmentStatus,queueStatus,
      provinceId,show,unifiedChannelId,broadbandStatus,
      keFuPhone,telNumber,shortName: shopData.shopShortName,
    })
  }
  if (indexData.configure == 1) {
    setTitle('店铺配置')
  } else {
    setTitle(share.value.shortTitle)
  }
  configBar()
}
function htmlStyle() {
  document.documentElement.style.fontSize = '37.5px'
  document.documentElement.style.width = '375px'
  document.documentElement.style.margin = '0 auto'
}
function openlogin(callback,showCancle) {
  callback = callback || logined
  indexData.showCancle = showCancle!==undefined ? showCancle :  indexData.showCancle
  let nalogin = getCookie('nalogin') == 1 ? false : true
  loginUtils.login(true,true,callback,false,nalogin,autoLoginCb,'0')
}
function getCmsData() {
  cmApi.getCmData(cmParam).then((res) => {
    if (res.code == 0 && res.data && res.data.cmData) {
      if (
        res.data.cmData.yundian_holiday_skin &&
        res.data.cmData.yundian_holiday_skin[0]
      ) {
        indexData.holidayskin = res.data.cmData.yundian_holiday_skin[0].title
      }

      if (
        res.data.cmData.yundian_holiday_skin &&
        res.data.cmData.yundian_holiday_skin[0]
      ) {
        indexData.myhtml = res.data.cmData.yundian_holiday_skin[0].rule_content
      }
    }
  })
}
const showMsgDialog = reactive({
  show:false
})
// 打开订阅弹框
const submsgrefs = ref(null)
function Submsg(){
  showMsgDialog.show = true
}

function checkSubscription(){
  if(proxy){
    proxy.$store.commit('SET_LOGINFIRST', 0)
  }
  submsgrefs.value.checkSubscription()
}
function getShowData(){
  shopAPI.getComponentQuery({shopId:indexData.pageInfo.shopId}).then((res)=>{
    if(res.code){
      Toast(res.message)
    }else{
      indexData.myShowData = res.data
    }
  })
}
init()
provide("getSon",{
  businessHoursfn,//店铺营业时间
  shopOpenStatusfn, //店铺营业状态
  pageInfofn, //获取页面数据
  provinceFn,
  openlogin, //打开登录
  getLogout,//退出登录
  shopData:queryShopData,
  Submsg:Submsg,
  myShowDatafn:myShowDatafn
})
</script>
<style>
* {
  box-sizing: border-box;
  outline: none;
}
body {
  background: #f1f1f1 !important;
  font-size: 12px;
}
.pt12 {
  padding-top: 12px;
}
.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.bgwhite {
  width: 100%;
  background: #fff;
}
.mt7 {
  margin-top: 7px !important;
}
a {
  color: #333333;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  user-select: none;
  -moz-user-focus: none;
}
.icpinfo {
  margin-bottom: 68px;
}
</style>
<style lang="scss" scoped>
.index {
  color: #333333;
  min-height: 101vh;
  // ::before{
  //   display: table;
  //   content: "";
  // }
  a {
    color: #333333;
  }
  .padding-top-34 {
    padding-top: 34px;
  }
}
</style>

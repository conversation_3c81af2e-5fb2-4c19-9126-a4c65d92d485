/*
 * @User: J<PERSON><PERSON><PERSON>
 * @FilePath: \yundian-m\src\api\login.js
 */
import request from "@/utils/request"
import store from "@/store"

export function login(username, password) {
  return request({
    url: "login.json",
    method: "get",
    data: {
      username,
      password
    }
  })
}

export function getUserInfo(token,userType,reqsource) {
  const url = new URL(location.href)
  let shopId = url.searchParams.get('shopId') || ""
  if(store.getters.shopId){
    shopId = store.getters.shopId
  }
  if(shopId=="null"){
    shopId=""
  }
  // console.log(userType,shopId,token)
  return request.post("/login/getUserInfoNew", {
    userType,shopId,token
  },
  {
    headers:{reqsource:reqsource,token},
    needEncrypt:1
  }
  )
}

// 根据sourceid获取移动认证大网token
export function getCmAuthTokenWU({ targetSourceId }) {
  return request({
    url: "/login/getCmAuthTokenWU",
    method: "post",
    data: { targetSourceId },
  })
}

export function logout() {
  return request({
    url: "/login/logOut",
    method: "get"
  })
}
/**
 * @description 用artifact换取用户token
 * @param {String} token 大网token
 * @param {String} paramType 登录类型
 * @return {{data:String,code:Number}}
 */
export function getToken({ artifact, type, token }) {
  const sendData = {
    artifact,
    type,
    paramType : '1'
  }

  if(token){
    sendData.paramType = '2'
    sendData.token = token
  }

  //用artifact换取token
  return request({
    url: "/login/getTokenByAllAttr",
    method: "post",
    data: sendData,
  })
}

/**
 * @description 企业微信拿到微信code换取用户token
 * @param {String} code
 * @return {{data:String,code:Number}}
 */
export function getWxTokenByCode(code){
  return request({
    url: "/wx/getWxCpUser",
    method: "post",
    data: { code },
  })
}
/**
 * @description 一键登录签名
 * @param {String} sign
 * @return {{data:String,code:Number}}
 */
export function cmccGetSign(presign) {
  return request({
    url: "/4glogin/getSign",
    method: "post",
    data:{
      presign
    }
  })
  //如果是链接php接口，返回值是res.data.sign
  // return request({
  //   url:'/../../ajax/user/usersign.json',
  //   data:{
  //     presign
  //   }
  // })
}
export function getAutoLoginToken() {
  //4g取号token
  return request({
    url: "/4glogin/getToken",
    method: "get",
  })
}
export function getAutoLoginInfo(token) {
  //4g取号token
  return request({
    url: "/4glogin/getPhoneInfo",
    method: "post",
    data: {token}
  })
}
export function getCanlogin(token) {
  //判断是否可以免登
  return request({
    url: "/4glogin/4GLogin",
    method: "get",
    params: {token}
  })
}
export function autoLogin({
  token,
  userInformation,
  msgId
}) {
  //免登
  return request({
    url: "/4glogin/auto4GLogin",
    method: "post",
    data: {
      token,
      userInformation,
      msgId
    }
  })
}
export default {
  getUserInfo,
  getToken,
  logout,
  autoLogin,
  getCanlogin,
  getAutoLoginInfo,
  getAutoLoginToken,
  getCmAuthTokenWU,
  cmccGetSign,
  getWxTokenByCode
}

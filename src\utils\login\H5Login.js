// h5登录
import UA from "../ua"
import Cookies from "js-cookie"
import {ORIGIN_ALLOWS} from "@/config/origin.js"

/**
 * @param {*} smsLogin 是否弹窗短信验证码登录，默认跳转到统一认证登录页
 */
const H5Login = async(loginUrl, channelID, smsLogin = false) => {
  let backOriginUrl = new URL(location.href)
  const index = ORIGIN_ALLOWS.indexOf(backOriginUrl.origin)
  let backUrl=""
  if (index === -1) {
    backUrl = encodeURIComponent("https://touch.10086.cn/yundian/nearby/index.html")
  }else{
    backUrl = encodeURIComponent(backOriginUrl)
  }

  if (smsLogin) {
    loginUrl +="/html/login/touch-sms.html"
  }
  const to = () => {window.location.href = `${loginUrl}?channelID=${channelID}&timestamp=${new Date().getTime() + Math.random() * 1000}&backUrl=${backUrl}`}

  const isWXMapp = await UA.isWeChatMiniApp()
  if(isWXMapp){

    const clearAllCookie = () => {
      localStorage.clear()
      sessionStorage.clear()
      const cookies = Cookies.get() // 获取所有的cookie
      for (const cookie in cookies) {
        Cookies.remove(cookie) // 删除每个cookie
      }
      Cookies.remove('nalogin')
      Cookies.remove('is_login')
      Cookies.remove('cmccssotoken')
      localStorage.removeItem('yundianToken')
    }

    clearAllCookie()
    setTimeout(() => {
      clearAllCookie()
      to()
    },1000)
  }else{
    to()
  }
}

export default H5Login

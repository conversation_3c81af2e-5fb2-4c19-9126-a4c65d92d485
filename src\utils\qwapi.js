import qwApi from '@/api/qw.js'
import UA from "@/utils/ua"
const appid = 'ww1df2e4de743def9a'
const wx = window.wx
/**
 * @method
 * @description  企微注入应用的身份和权限
*/
export const wechartWorkInit = async() => {
  if (UA.isWechat && UA.isWechatWork) {
    // if (!UA.isWechat) {//调试
    const configOptions = await qwApi.getWxAgentConfig({ url: location.href })
    wx.config({
      beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: configOptions.data.corpid, // 必填，企业微信的corpID
      timestamp: configOptions.data.timestamp, // 必填，生成签名的时间戳
      nonceStr: configOptions.data.nonceStr, // 必填，生成签名的随机串
      signature: configOptions.data.signature,// 必填，签名，见附录1
      jsApiList: ['shareToExternalContact','openEnterpriseChat'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    })
    wx.ready(function() {
      // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，
      // config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。
      // 对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
      // 调用agentConfig前，需要先成功调用config,所以可以吧agentConfig的调用写在wx.ready里面
      console.log(wx.agentConfig)
      wx.agentConfig({
        corpid: configOptions.data.corpid, // 必填，企业微信的corpid，必须与当前登录的企业一致
        agentid: configOptions.data.agentid, // 必填，企业微信的应用id （e.g. 1000247）
        timestamp: configOptions.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: configOptions.data.nonceStr, // 必填，生成签名的随机串
        signature: configOptions.data.agentSignature,// 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ['shareToExternalContact', 'getCurExternalContact', 'selectExternalContact', 'selectEnterpriseContact','sendChatMessage','getContext','openEnterpriseChat'], //必填
        success: function(res) {
          // 回调
          alert("wx.agentConfig调用成功")
          wx.checkJsApi({
            jsApiList: ['shareToExternalContact', 'getCurExternalContact', 'selectExternalContact', 'selectEnterpriseContact','sendChatMessage','getContext','openEnterpriseChat'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
            success: function(res) {
              // 以键值对的形式返回，可用的api值true，不可用为false
              // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
              console.log(res, 'agentConfig_callback')
            }
          })
        },
        fail: function(res) {
          alert("wx.agentConfig调用失败")
          console.log(res, 'agentConfig_callback')
          if (res.errMsg.indexOf('function not exist') > -1) {
            alert('版本过低请升级')
          }
        }
      })
    })
  }
}
/**
 * @method
 * @description  企微请求后端获取查询外部联系人
 * @param {Object} req
 * @param {string} req.phone
*/
export const getWxCustomer = (phone) => {
  return qwApi.getWxCustomer({ phone })
}

/**
 * @method
 * @description  企微打开客户会话
 * @param {Object} req
 * @param {string} req.userIds - 参与会话的企业成员列表
 * @param {string} req.externalUserIds - 参与会话的外部联系人列表
 * @param {string} req.groupName - 会话名称。单聊时该参数传入空字符串""即可
*/
export const openEnterpriseChat = ({ userIds = '', externalUserIds = '', groupName = '' }) => {
  return new Promise((resolve, reject) => {
    wx.openEnterpriseChat({
      // 注意：userIds和externalUserIds至少选填一个。内部群最多2000人；外部群最多500人；如果有微信联系人，最多40人
      userIds: userIds,    //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
      externalUserIds: externalUserIds, // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
      groupName: groupName,  // 会话名称。单聊时该参数传入空字符串""即可。
      chatId: '', // 若要打开已有会话，需指定此参数。如果是新建会话，chatId必须为空串
      success: function(res) {
        // var chatId = res.chatId; //返回当前群聊ID，仅当使用agentConfig注入该接口权限时才返回chatId
        // 回调
        console.log(['打开成功', res])
        resolve(res)
      },
      fail: function(res) {
        if (res.errMsg.indexOf('function not exist') > -1) {
          alert('版本过低请升级')
        }
        console.log(['打开失败', res])
        reject(res)
      }
    })
  })
}

/**
 * @method
 * @description  企微群发消息
 * @param {Object} req
 * @param {string} req.content - 发送消息内容
 * @param {string} req.userId - 发送消息的用户id
*/
export const shareToExternalContact = ({ userId,url,title,desc,imgUrl }) => {
  wx.invoke("shareToExternalContact", {
    externalUserIds: userId,//["wmEAlECwAAHrbAAAOK5u3Bf13xlYDAAA", "wmEAlECwAAHiBBBDOK5u3Af13xlYDBBB"],//客户列表，从4.1.10版本开始支持（mac端不支持）
    attachments: [
      {
        msgtype: "link",    // 消息类型，必填
        link: {
          title,        // H5消息标题
          imgUrl,    // H5消息封面图片URL
          desc,    // H5消息摘要
          url,        // H5消息页面url 必填
        },
      }
    ]
  }, function(res) {
    console.log(["发送成功", res])
    if (res.err_msg == "shareToExternalContact:ok") {
      console.log(["发送成功", res])
    }
  }
  )
}

/**
 * @method
 * @description  企微发送消息到当前会话
 * @param {Object} req
 * @param {string} req.content - 发送消息内容
 * @param {string} req.userId - 发送消息的用户id
*/
export const sendChatMessage = ({ link,title,desc,imgUrl}) => {
  wx.invoke("sendChatMessage", {
    msgtype:"news", //消息类型，必填
    enterChat: true, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
    news: {
      link, //H5消息页面url 必填
      title, //H5消息标题
      desc, //H5消息摘要
      imgUrl, //H5消息封面图片URL
    },
  }, function(res) {
    console.log(["发送成功", res])
  })
}
/**
 * @method
 * @description  获取当前页面打开场景
*/
export const getContext = ()=>{
  return new Promise((resolve, reject) => {
    wx.invoke('getContext', {
    }, function(res){
      if(res.err_msg == "getContext:ok"){
        let entry  = res.entry //返回进入H5页面的入口类型，目前有normal、contact_profile、single_chat_tools、group_chat_tools
        resolve(entry)
      }else {
        reject(res)
      }
    })
  })
}

/**
 * @method
 * @description  获取当前外部联系人userid
 * @param {Object} req
*/
export const getUserId = () => {
  return new Promise((resolve, reject) => {
    wx.invoke('getCurExternalContact', {}, function(res) {
      if (res.err_msg == 'getCurExternalContact:ok') {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
}
/**
 * @method
 * @description  获取外部联系人选人接口
 * @param {Object} req
*/
export const getUserIds = () => {
  return new Promise((resolve) => {
    wx.invoke('selectExternalContact', {
      "filterType": 0 //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人
    }, function(res) {
      if (res.err_msg == 'selectExternalContact:ok') {
        resolve(res)
      } else {
        resolve()
      }
    })
  })
}

/**
 * @method
 * @description  获取通讯录选人接口
 * @param {Object} req
*/
export const getQwUserIds = () => {
  return new Promise((resolve, reject) => {
    wx.invoke('selectEnterpriseContact', {
      "fromDepartmentId": -1,// 必填，表示打开的通讯录从指定的部门开始展示，-1表示自己所在部门开始, 0表示从最上层开始
      "mode": "multi",// 必填，选择模式，single表示单选，multi表示多选
      "type": ["department", "user"],// 必填，选择限制类型，指定department、user中的一个或者多个
      "selectedDepartmentIds": ["2","3"],// 非必填，已选部门ID列表。用于多次选人时可重入，single模式下请勿填入多个id
      "selectedUserIds": ["lisi","lisi2"]// 非必填，已选用户ID列表。用于多次选人时可重入，single模式下请勿填入多个id
    },function(res){
      console.log(res)
    })
  })
}

export default {
  wechartWorkInit,
  getWxCustomer,
  openEnterpriseChat,
  shareToExternalContact,
  getUserId,
  getUserIds,
  getQwUserIds,
  sendChatMessage,
  getContext
}

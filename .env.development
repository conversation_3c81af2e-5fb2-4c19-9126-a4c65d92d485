# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/dev-api'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
PROXY_IP="https://*********"
PROXY_M="m.staff.ydsc.liuliangjia.cn"
PROXY_IMG1="img1.staff.ydsc.liuliangjia.cn"
#插码引用链接
VUE_APP_ASPSC_DOMAIN='img0.staff.ydsc.liuliangjia.cn'
#泛全域名
VUE_APP_B2B_DOMAIN='https://b2c.cmccsa.cn'
#当前域名
VUE_APP_ORIGIN_DOMAIN='https://m.staff.ydsc.liuliangjia.cn'
#企业微信登录agentid staff环境可以代理生产
VUE_APP_AGENTID='1000006'

import axios from "axios"
import { getToken, removeLocalStorage } from "@/utils/login/localStorage"
import loginUtils from "@/utils/login"
import transitEncrypt from "./transit-encrypt.js"
// import Vue from "vue"

// const vm = new Vue()
global.BASE_URL = "/yundian/netShop/"
/* global BASE_URL */
/* eslint no-undef: "error" */
// create an axios instance
const request = axios.create({
  baseURL: BASE_URL, // api 的 base_url
  timeout: 30000, // request timeout
  method: "post",
  headers: {
    "Content-type": "application/json; charset=utf-8",
    // post: {
    //   accessToken: "index"
    // }
  },
})

// request interceptor
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    const token = getToken()
    if (token && !config.headers["token"]) {
      config.headers["token"] = token
    }
    if (!config.headers["reqsource"]) {
      config.headers["reqsource"] = 1
    }
    if (config.needEncrypt) {
      config.authKey = transitEncrypt.getRandomAesKey()
      config.headers['leaf'] = transitEncrypt.encryptRsa(config.authKey)
      config.data = {
        encrypt: transitEncrypt.encryptAes(config.data,config.authKey)
      }
    }
    return config
  },
  (error) => {
    // Do something with request error
    // console.log(error) // for debug
    Promise.reject(error)
  }
)

// response interceptor
request.interceptors.response.use(
  (response) => {
    let res = response.data
    if (response.config.responseType == "arraybuffer") {
      if (response.data.byteLength < 1024) {
        res = ab2Json(response.data)
      } else {
        res = {
          code: 0,
          data: {
            headers: response.headers,
            body: response.data,
          },
        }
      }
    }
    if (res.code === 2 || res.code === 9199) {
      // console.log(res.code)
      removeLocalStorage()
      loginUtils.login(true, true, () => {
        location.reload()
      })
    } else {
      if(response.config.authKey && res.data) {
        res.data = JSON.parse(transitEncrypt.decryptAes(res.data,response.config.authKey))
      }
      return res
    }
  },
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 xmlhttprequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  (error) => {
    // console.log("err" + error) // for debug
    let msg = "网络出错，请稍后再试!"
    return Promise.resolve({
      code: 1,
      msg: msg,
      omsg: error,
    })
  }
)
function ab2Json(buf) {
  let encodedString = String.fromCodePoint.apply(null, new Uint8Array(buf))
  let decodedString = decodeURIComponent(escape(encodedString)) //没有这一步中文会乱码
  return JSON.parse(decodedString)
}
export default request

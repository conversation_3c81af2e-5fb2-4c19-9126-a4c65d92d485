import request from "@/utils/request"
/**
 * @method
 * @description  企微请求后端获取config信息
 * @param {Object} req
 * @param {string} req.url url
*/
export function getWxAgentConfig({
  url
}) {
  return request({
    url:"/wx/getWxAgentConfig",
    method: "post",
    data: { url },
  })
}
/**
 * @method
 * @description  企微请求后端获取查询外部联系人
 * @param {Object} req
 * @param {string} req.phone
*/
export function getWxCustomer({phone}) {
  return request({
    url:"/wx/getWxCustomer",
    method: "post",
    data: {phone}
  })
}

export default {
  getWxAgentConfig,
  getWxCustomer
}

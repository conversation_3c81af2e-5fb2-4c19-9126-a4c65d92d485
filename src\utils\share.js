// 分享功能
import UA from "@/utils/ua"
import leadeonLoader from "@/utils/leadeonloader"
import html2canvas from "html2canvas"
import shareApi from "@/api/share"
import {ENV} from "@/utils/env.js"
// import { getWxConfig } from "@/api/wx"

// const leadeon = window.leadeon
let shareInfo = {
  url: location.href,
  title: document.title
}
const wx = window.wx
let callback = null
/**
 * 分享内容
 * @param {*} imgUrl 分享图标
 * @param {*} title 主标题
 * @param {*} desc 副标题(描述文案)
 */
const share =  async({ imgUrl, title, desc, url = location.href.split("#")[0] ,unifiedChannelId},callbackfn) => {
  if(callbackfn){
    callback = callbackfn
  }
  console.log({ imgUrl, title, desc, url ,unifiedChannelId},222222222)
  // app分享
  if (UA.isApp) {
    let shortUrl = await getShortUrl(url)
    try {
      appShare({ imgUrl, title, desc, url:shortUrl })
      //客户端分享
      appH5Share({ imgUrl, title, desc, url:shortUrl })
    } catch (err) {
      return false
    }
  } else if (UA.isWechat || UA.isWechatWork) {
    // 微信分享
    // wxShare({ imgUrl, title, desc, shortUrl })
    await changeWxShareConfig({ imgUrl, title, desc, url ,unifiedChannelId},callback)
  }
}

function getShortUrl(url){
  return shareApi.getShortUrl({url:url}).then((res)=>{
    if(res.data && res.data.url ){
      return res.data.url
    }else{
      return url
    }
  }).catch((error)=>{
    // console.log(error)
    return url
  })
}
// 端内配置右上角分享
const appShare = async({
  imgUrl,
  title,
  desc,
  url = location.href.split("#")[0]
}) => {
  const leadeon = await leadeonLoader()
  return new Promise(resolve=>{
    leadeon.enableShared({
      debug: false,
      enable: true,
      shareObj: {
        title,
        link: url,
        imgUrl,
        content: desc
      }, //必填参数，分享内容对象，参见 shareMessage 参数，title、link、imgUrl、content不能为空.
      success: function(res) {
        // res为空
        resolve(res)
      },
      error: function(res) {
        resolve(res)
      }
    })
  })

}

// 隐藏分享按钮-V5.1 新增
export const hiddenShare =  async() => {
  const leadeon = await leadeonLoader()
  leadeon.hideShareButton({
    debug: false,
    success: function(res) {
      //res为空
    },
    error: function(res) {}
  })
}

// / 端内分享页面
const appH5Share = async({
  imgUrl,
  title,
  desc,
  url = location.href.split("#")[0]
}) => {
  const leadeon = await leadeonLoader()
  leadeon.shareMessage({
    debug: false,
    title, //分享标题 -必传(V4.3变更)
    link: url, //分享链接,参数中如果有中文，需要对参数进行编码 -必传(V4.3变更)
    imgUrl, //分享图标 -必传(V4.3变更)
    content: desc, //分享内容 -必传(V4.3变更)
    type: 'link',
    shareChannelArray:['0','1','2','4','5'] ,   //分享渠道
    success: function(res) {
      if(callback!=null){
        callback('success')
      }
    },
    error: function(res) {
      if(callback!=null){
        callback()
      }
    }
  })
}

// 端内分享小程序
const appMiniShare = async({
  imgUrl,
  title,
  desc,
  url = location.href.split("#")[0]
}) => {
  const leadeon = await leadeonLoader()

  leadeon.shareMessage({
    debug: false,
    title, //分享标题 -必传(V4.3变更)
    link: url, //分享链接,参数中如果有中文，需要对参数进行编码 -必传(V4.3变更)
    imgUrl, //分享图标 -必传(V4.3变更)
    content: desc, //分享内容 -必传(V4.3变更)
    shareChannelArray: ["1", "3", "6"],

    //微信小程序设置
    wx: {
      userName: "gh_77a83b248df1", //小程序的username
      path: `/moduleJiYunActivity/pages/zhongqiuhuodong/zhongqiuhuodong?url=${encodeURIComponent(
        url
      )}`, //打开小程序指定页面路径
      miniProgramType: "0", //0 正式版 1 开发板 2体验版
      webpageUrl: url, //若微信版本太低不支持小程序，打开此地址
      withShareTicket: "1", //0 关闭 1 打开。默认传1即可
      hdImageData: imgUrl, //限制大小不超过128KB,，只支持png图片，别的图片类型不支持，卡片预览图 5:4
      title: desc,
      description: desc
    },

    success: function(res) {},
    error: function(res) {}
  })
}

// 普通微信分享（修改参数）
const changeWxShareConfig = async(
  {
    imgUrl,
    title,
    desc,
    url = location.href.split("#")[0],
    unifiedChannelId
  },fn
)=>{
  shareInfo = {
    imgUrl,
    title,
    desc,
    url:await getShortUrl(url),
    unifiedChannelId
  }
  callback = fn
  //小程序分享时抛出数据
  const isWXMapp = await UA.isWeChatMiniApp()
  if(isWXMapp){
    shareInfo.miniProgramUrl = url+'&WT.mc_id=YD_XCX_'+shareInfo.unifiedChannelId
    wx.miniProgram.postMessage({
      data: {
        'action': 'share-process',
        'share': shareInfo
      }
    },ENV.getOrigin())
  }
  if(callback&&callback!=null){
    callback('success')
  }
}
// 微信分享样式
//var imgUrl = document.querySelectorAll('body > div')[0].querySelectorAll('img')[0].src; // 分享后展示的一张图片
//var lineLink = 'http://touch.10086.cn/hd/gotoextend'; // 点击分享后跳转的页面地址
var appid = '' // 应用id,如果有可以填，没有就留空
function shareFriend() {
  window.WeixinJSBridge.invoke('sendAppMessage', {
    'appid': appid,
    'img_url': shareInfo.imgUrl,
    'img_width': '200',
    'img_height': '200',
    'link': shareInfo.url,
    'desc': shareInfo.desc,
    'title': shareInfo.title
  }, function(res) {
    if(callback!=null){
      callback('success')
    }
    //_report('send_msg', res.err_msg); // 这是回调函数，必须注释掉
  })
}

function shareTimeline() {
  window.WeixinJSBridge.invoke('shareTimeline', {
    'img_url': shareInfo.imgUrl,
    'img_width': '200',
    'img_height': '200',
    'link': shareInfo.url,
    'desc': shareInfo.desc,
    'title': shareInfo.title
  }, function(res) {
    if(callback!=null){
      callback('success')
    }
    //_report('timeline', res.err_msg); // 这是回调函数，必须注释掉
  })
}

function shareWeibo() {
  window.WeixinJSBridge.invoke('shareWeibo', {
    'content':  shareInfo.desc,
    'url':  shareInfo.url,
  }, function(res) {
    if(callback!=null){
      callback('success')
    }
    //_report('weibo', res.err_msg);
  })
}

// 当微信内置浏览器完成内部初始化后会触发WeixinJSBridgeReady事件。
document.addEventListener('WeixinJSBridgeReady',function() {
  // 发送给好友
  window.WeixinJSBridge.on('menu:share:appmessage', function(argv) {
    shareFriend()
  })
  // 分享到朋友圈
  window.WeixinJSBridge.on('menu:share:timeline', function(argv) {
    shareTimeline()
  })
  // 分享到微博
  window.WeixinJSBridge.on('menu:share:weibo', function(argv) {
    shareWeibo()
  })

}, false)

//企微分享
const qwShare = (callback)=>{
  let url = new URL(shareInfo.url)
  url.searchParams.set('WT.mc_id','YunD@qiwei')
  wx.onMenuShareWechat({
    title: shareInfo.title, // 分享标题
    desc: shareInfo.desc, // 分享描述
    link:  url.href, // 分享链接
    imgUrl: shareInfo.imgUrl, // 分享图标
    success: function() {
      alert('分享成功')
      callback && callback()
      // 用户确认分享后执行的回调函数
    },
    cancel: function() {
      alert('取消分享')
      // 用户取消分享后执行的回调函数
    }
  })
  wx.onMenuShareAppMessage({
    title: shareInfo.title, // 分享标题
    desc: shareInfo.desc, // 分享描述
    link:  url.href, // 分享链接
    imgUrl: shareInfo.imgUrl, // 分享图标
    success: function() {
      alert('分享成功')
      callback && callback()
      // 用户确认分享后执行的回调函数
    },
    cancel: function() {
      alert('取消分享')
      callback && callback()
      // 用户取消分享后执行的回调函数
    }
  })
  wx.onMenuShareTimeline({
    title: shareInfo.title, // 分享标题
    desc: shareInfo.desc, // 分享描述
    link:  url.href, // 分享链接
    imgUrl: shareInfo.imgUrl, // 分享图标
    success: function() {
      callback && callback()
      alert('分享成功')
      // 用户确认分享后执行的回调函数
    },
    cancel: function() {
      alert('取消分享')
      // 用户取消分享后执行的回调函数
    }
  })
}
// 获取进入场景
const getContext = (callback)=>{
  wx.invoke('getContext', {
  }, function(res){
    if(res.err_msg == "getContext:ok"){
      let entry  = res.entry //返回进入H5页面的入口类型，目前有normal、contact_profile、single_chat_tools、group_chat_tools
      callback && callback(entry)
    }else {
      callback("error")
    }
  })
}
const sendChatMessage = ()=>{
  let url = new URL(shareInfo.url)
  url.searchParams.set('WT.mc_id','YunD@qiwei')
  wx.invoke('sendChatMessage', {
    msgtype:"news", //消息类型，必填
    news:
    {
      title: shareInfo.title, // 分享标题
      desc: shareInfo.desc, // 分享描述
      link:  url.href, // 分享链接
      imgUrl: shareInfo.imgUrl // 分享图标
    }
  }, function(res) {
    if (res.err_msg == 'sendChatMessage:ok') {
      wx.closeWindow()
    }
  })
}
const wechartWorkInit = ({province_id})=>{
  if (UA.isWechat && UA.isWechatWork) {
    shareApi.getSign({code:province_id,url:location.href}).then(r=>{
      wx.config({
        beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: r.data.appId, // 必填，企业微信的corpID
        timestamp: r.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: r.data.nonceStr, // 必填，生成签名的随机串
        signature: r.data.signature,// 必填，签名，见附录1
        jsApiList: ['onMenuShareAppMessage','onMenuShareWechat','onMenuShareTimeline','shareAppMessage','shareWechatMessage','closeWindow'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
      })
      shareApi.getAgentJsSdk({corpId:r.data.appId,code:province_id,url:location.href}).then(r=>{
        wx.agentConfig({
          corpid: r.data.corpId, // 必填，企业微信的corpid，必须与当前登录的企业一致
          agentid: r.data.agentId, // 必填，企业微信的应用id （e.g. 1000247）
          timestamp: r.data.timestamp, // 必填，生成签名的时间戳
          nonceStr: r.data.nonceStr, // 必填，生成签名的随机串
          signature: r.data.signature,// 必填，签名，见附录-JS-SDK使用权限签名算法
          jsApiList: ['selectExternalContact','sendChatMessage','getContext'], //必填
          success: function(res) {
            // 回调
            //alert("wx.agentConfig调用成功")
            wx.checkJsApi({
              jsApiList: ['onMenuShareAppMessage','onMenuShareWechat','onMenuShareTimeline','sendChatMessage','getContext'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
              success: function(res) {
                // 以键值对的形式返回，可用的api值true，不可用为false
                // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
              }
            })
          },
          fail: function(res) {
            if(res.errMsg.indexOf('function not exist') > -1){
              alert('版本过低请升级')
            }
          }
        })
      })
      wx.ready(function(){
        // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
        qwShare()
      })
    })
  }
}
//获取图片
export const getImg = (
  targetDom
)=>{
  // //滚动置顶，解决滚动后截屏截不全的问题
  // window.pageYoffset = 0
  // document.documentElement.scrollTop = 0
  // document.body.scrollTop = 0
  return new Promise((resolve,reject)=>{
    html2canvas(targetDom, {
      allowTaint: true,
      useCORS: true,
      height: targetDom.clientHeight,
      width: targetDom.clientWidth,
      scrollY: 0,
      scrollX: 0,
      // scrollY:1, //以下三项是为了填滚动截屏 截不全的坑
      // y:1,
      scale:1
    }).then(canvas => {
      let dataURL = canvas.toDataURL("image/png")
      resolve(dataURL)
    }).catch(error=>{
      reject(error)
    })
  })
}

// 保存图片
export const saveImg = (
  targetDom,
  fn
)=>{
  //滚动置顶，解决滚动后截屏截不全的问题
  window.pageYoffset = 0
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0
  html2canvas(targetDom, {
    allowTaint: true,
    useCORS: true,
    height: targetDom.clientHeight,
    width: targetDom.clientWidth,
    scrollY: 0,
    scrollX: 0,
    // scrollY:1, //以下三项是为了填滚动截屏 截不全的坑
    // y:1,
    scale:1
  }).then(canvas => {
    let dataURL = canvas.toDataURL("image/png")
    if (UA.isApp) {
      savePhoto(dataURL)
      fn()
    }else {
      var a = document.createElement('a')
      a.href = dataURL
      a.download="qrcode.png"
      a.click()
      fn()
    }
  })
}
// 客户端保存图片
export const savePhoto = async(
  dataURL
)=> {
  const leadeon = await leadeonLoader()
  leadeon.savePhoto({
    debug: false,
    type: 2, //图片传参方式：1表示url方式，2表示base64方式,3表示pdf方式（只对安卓有效）
    data: dataURL, //根据type值不同，data为url链接或者base64数据
    phoneNum: 'qrcode.png', //手机号（只有android使用，用于图片命名）
    success: function(res) {
      // var sdUrl = res.sdUrl //sd卡存储路径
    },
    error: function(res) {}
  })
}

export default {
  share,
  appShare,
  appH5Share,
  appMiniShare,
  // wxShare,
  qwShare,
  getContext,
  sendChatMessage,
  wechartWorkInit,
  getImg,
  saveImg,
  savePhoto,
  changeWxShareConfig,
  hiddenShare
}

/*
 * @User: JOJO
 * @FilePath: \yundian-m\src\utils\login\getUserInfo.js
 */
import { setLocalStorage } from "./localStorage"
import loginApi from "@/api/login"
import { addUID } from "./tysso"
import store from '../../store'

/**
 * artifact换取用户信息
 * @param {*} params mobileNo, smsCode, userId
 * @param {*} sucCb 成功后的回调函数
 * @param {*} failCb 失败后的回调函数
 */
export const getUserInfo = (params, sucCb, failCb) => {
  loginApi
    .getUserInfo(params)
    .then((res = {}) => {
      if (res.code == 0) {
        // 成功换取用户信息
        setLocalStorage(res.data)
        sucCb && sucCb(res.data)
      } else {
        failCb && failCb(res)
      }
    })
    .catch(err => {
      failCb && failCb(err)
    })
}

export const getUserInfoByArtifact = (artifact, sucCb, failCb,userType,token,reqsource) => {
  return new Promise(resolve => {
    const type = new URL(location.href).searchParams.get('type') || '00'
    loginApi
      .getToken({artifact,type,token})
      .then(async(res = {}) => {
        if (res.code == 0 && res.data && res.data.token) {

          // 如果返回了artifact, 就直接用返回的artifact
          if(res.data.artifact){
            addUID(res.data.artifact)
          }
          // 成功换取用户信息
          let userInfo = await loginApi.getUserInfo(res.data.token,userType,reqsource).then((res)=>{
            // console.log(res)
            if (res.code == 0) {
              return res.data
            }else{
              return null
            }
          })
          store.commit('SET_LOGINFIRST',1)
          store.commit('SET_USERINFO',userInfo)
          store.commit('SET_USERNAME',res.data.UserName)
          localStorage.setItem('userName', res.data.UserName)
          setLocalStorage(res.data)
          sucCb && sucCb(userInfo?userInfo:res.data)
          resolve({
            status: 0, // 0 成功 1 失败
            data: res.data
          })
        // } else if ([200, 201, 202, 203, 204].indexOf(Number(res.code)) > -1) {
        //   // 限流
        //   goHot()
        } else {
          /**
           * @todo 修改了failcb的入参，之前是err,但是未定义，需要验证
           */
          failCb && failCb({ status: 1 })
          resolve({ status: 1 })
        }
      })
      .catch(err => {
        failCb && failCb(err)
        resolve({ status: 1 })
      })
  })
}

// const goHot = () => {
//   let url = new URL(location.href),
//     params = url.searchParams
//   params.delete("round")
//   params.delete("artifact")
//   let indexUrl = url
//     .toString()
//     .replace("raffle.html", "hot.html")
//     .replace("index.html", "hot.html")
//   location.href = indexUrl
// }
